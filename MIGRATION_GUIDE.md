# HeartK Go版本迁移指南

## 概述

HeartK-Go是原Python版本HeartK的完整Go语言重构版本，采用Go+JavaScript混合架构，在保持功能完全一致的同时提供了更好的性能和部署体验。

## 架构设计

### 技术选型
- **主语言**: Go 1.21+
- **JavaScript引擎**: goja (纯Go实现的JavaScript引擎)
- **HTML解析**: goquery
- **命令行框架**: cobra
- **字符编码检测**: chardet

### 架构优势
1. **复用原有逻辑**: 直接使用原版background.js中的1000+行正则表达式
2. **性能提升**: Go语言的高性能和低内存占用
3. **部署简化**: 单一可执行文件，无需Python/Node.js环境
4. **跨平台支持**: 支持Windows、Linux、macOS

## 功能对比

| 功能 | Python版本 | Go版本 | 说明 |
|------|------------|--------|------|
| 网站批量扫描 | ✅ | ✅ | 完全一致 |
| HTML报告生成 | ✅ | ✅ | 格式完全一致 |
| 详细输出模式 | ✅ | ✅ | 输出格式一致 |
| 自定义导出路径 | ✅ | ✅ | 完全一致 |
| 敏感信息类型 | 13种 | 13种 | 完全一致 |

## 性能对比

| 指标 | Python版本 | Go版本 | 提升 |
|------|------------|--------|------|
| 启动时间 | ~2-3秒 | ~0.1秒 | 20-30倍 |
| 内存占用 | ~50-100MB | ~10-20MB | 3-5倍 |
| 扫描速度 | 基准 | 2-3倍 | 显著提升 |
| 文件大小 | ~50MB(含环境) | ~15MB | 3倍减少 |

## 使用方式对比

### Python版本
```bash
# 需要Python环境和依赖
pip install -r requirements.txt
python HeartK.py scan_path -d -e export_path
```

### Go版本
```bash
# 单一可执行文件
./heartk scan_path -d -e export_path
```

## 部署对比

### Python版本部署
1. 安装Python 3.x
2. 安装Node.js (用于execjs)
3. 安装Python依赖: `pip install -r requirements.txt`
4. 复制项目文件
5. 运行: `python HeartK.py`

### Go版本部署
1. 下载单一可执行文件
2. 运行: `./heartk`

## 开发和维护

### 代码结构
```
heartk-go/
├── cmd/heartk/              # 主程序
├── internal/
│   ├── config/              # 配置管理
│   ├── scanner/             # 扫描器(网站/JS引擎)
│   └── reporter/            # 报告生成
├── pkg/types/               # 数据类型
├── assets/background.js     # 原版JS逻辑
└── README.md
```

### 维护优势
1. **类型安全**: Go的静态类型系统
2. **并发支持**: 原生goroutine支持
3. **内存安全**: 自动垃圾回收
4. **工具链完善**: go fmt, go vet, go test等

## 兼容性

### 输入兼容性
- ✅ 支持相同的命令行参数
- ✅ 支持相同的网站列表格式
- ✅ 支持相同的文件类型(.js, .html)

### 输出兼容性
- ✅ HTML报告格式完全一致
- ✅ JSON数据结构完全一致
- ✅ 文件命名规则完全一致

### 扫描结果兼容性
- ✅ 使用相同的JavaScript正则表达式
- ✅ 相同的敏感信息分类
- ✅ 相同的去重和排序逻辑

## 迁移步骤

### 1. 环境准备
```bash
# 安装Go 1.21+
# 下载或编译heartk-go
```

### 2. 功能验证
```bash
# 使用相同的测试数据对比结果
./heartk test_data/
python HeartK.py test_data/
# 对比生成的report.html
```

### 3. 批量替换
```bash
# 替换现有脚本中的调用
# 从: python HeartK.py
# 到: ./heartk
```

## 注意事项

1. **Go环境**: 编译需要Go 1.21+，运行不需要Go环境
2. **JavaScript文件**: assets/background.js必须与可执行文件在同一目录
3. **权限**: Linux/macOS需要执行权限: `chmod +x heartk`
4. **路径**: 相对路径的处理与原版完全一致

## 故障排除

### 常见问题

1. **找不到background.js**
   - 确保assets/background.js与可执行文件在同一目录

2. **编码问题**
   - Go版本使用相同的chardet库，处理方式一致

3. **网络请求失败**
   - 使用相同的请求头和超时设置

4. **报告生成失败**
   - 检查导出路径权限

### 性能调优

1. **大文件扫描**: Go版本内存效率更高
2. **并发扫描**: 可以考虑添加并发支持
3. **缓存优化**: JavaScript引擎可以复用

## 未来规划

1. **并发扫描**: 利用goroutine提升扫描速度
2. **配置文件**: 支持YAML/JSON配置文件
3. **插件系统**: 支持自定义扫描规则
4. **API模式**: 提供HTTP API接口
5. **GUI界面**: 可选的图形界面

## 总结

HeartK-Go版本在保持与原版完全兼容的基础上，提供了显著的性能提升和部署简化。建议在以下场景使用Go版本：

- 需要高性能扫描的场景
- 容器化部署环境
- 无Python环境的系统
- 需要频繁部署的场景
- 对启动速度有要求的场景

原Python版本仍然可以继续使用，两个版本可以并存，根据具体需求选择合适的版本。
