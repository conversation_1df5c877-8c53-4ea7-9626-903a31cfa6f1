@echo off
chcp 65001 >nul

echo 开始构建 HeartK-Go...

REM 检查Go环境
where go >nul 2>nul
if %errorlevel% neq 0 (
    echo 错误: 未找到Go环境，请先安装Go 1.21或更高版本
    pause
    exit /b 1
)

REM 检查Go版本
for /f "tokens=3" %%i in ('go version') do set GO_VERSION=%%i
set GO_VERSION=%GO_VERSION:go=%
echo Go版本检查通过: %GO_VERSION%

REM 下载依赖
echo 下载依赖包...
go mod tidy
if %errorlevel% neq 0 (
    echo 错误: 依赖下载失败
    pause
    exit /b 1
)

REM 构建项目
echo 构建项目...
go build -o heartk.exe cmd/heartk/main.go
if %errorlevel% equ 0 (
    echo 构建成功! 可执行文件: heartk.exe
    echo.
    echo 使用方法:
    echo   heartk.exe [扫描路径]                    # 基本扫描
    echo   heartk.exe [扫描路径] -d                 # 详细输出
    echo   heartk.exe [扫描路径] -e [导出路径]      # 指定导出路径
    echo.
    echo 示例:
    echo   heartk.exe C:\path\to\scan               # 扫描目录
    echo   heartk.exe file.js                      # 扫描单个文件
    echo   heartk.exe websites.txt                 # 批量扫描网站
) else (
    echo 构建失败!
    pause
    exit /b 1
)

pause
