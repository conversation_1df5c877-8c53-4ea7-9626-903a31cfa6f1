#!/bin/bash

# HeartK-Go 构建脚本

echo "开始构建 HeartK-Go..."

# 检查Go环境
if ! command -v go &> /dev/null; then
    echo "错误: 未找到Go环境，请先安装Go 1.21或更高版本"
    exit 1
fi

# 检查Go版本
GO_VERSION=$(go version | awk '{print $3}' | sed 's/go//')
REQUIRED_VERSION="1.21"

if [ "$(printf '%s\n' "$REQUIRED_VERSION" "$GO_VERSION" | sort -V | head -n1)" != "$REQUIRED_VERSION" ]; then
    echo "错误: Go版本过低，需要1.21或更高版本，当前版本: $GO_VERSION"
    exit 1
fi

echo "Go版本检查通过: $GO_VERSION"

# 下载依赖
echo "下载依赖包..."
go mod tidy

if [ $? -ne 0 ]; then
    echo "错误: 依赖下载失败"
    exit 1
fi

# 构建项目
echo "构建项目..."
go build -o heartk cmd/heartk/main.go

if [ $? -eq 0 ]; then
    echo "构建成功! 可执行文件: ./heartk"
    echo ""
    echo "使用方法:"
    echo "  ./heartk [扫描路径]                    # 基本扫描"
    echo "  ./heartk [扫描路径] -d                 # 详细输出"
    echo "  ./heartk [扫描路径] -e [导出路径]      # 指定导出路径"
    echo ""
    echo "示例:"
    echo "  ./heartk /path/to/scan                 # 扫描目录"
    echo "  ./heartk file.js                      # 扫描单个文件"
    echo "  ./heartk websites.txt                 # 批量扫描网站"
else
    echo "构建失败!"
    exit 1
fi
