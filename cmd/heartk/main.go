package main

import (
	"bufio"
	"fmt"
	"os"
	"strings"

	"heartk-go/internal/config"
	"heartk-go/internal/reporter"
	"heartk-go/internal/scanner"
	"heartk-go/pkg/types"

	"github.com/spf13/cobra"
)

var (
	verbose    bool
	exportPath string
)

func main() {
	// 打印ASCII艺术字
	fmt.Print(` ___  ___      _______       ________      ________      _________    ___  __       
|\  \|\  \    |\  ___ \     |\   __  \    |\   __  \    |\___   ___\ |\  \|\  \     
\ \  \\\  \   \ \   __/|    \ \  \|\  \   \ \  \|\  \   \|___ \  \_| \ \  \/  /|_   
 \ \   __  \   \ \  \_|/__   \ \   __  \   \ \   _  _\       \ \  \   \ \   ___  \  
  \ \  \ \  \   \ \  \_|\ \   \ \  \ \  \   \ \  \\  \|       \ \  \   \ \  \\ \  \ 
   \ \__\ \__\   \ \_______\   \ \__\ \__\   \ \__\\ _\        \ \__\   \ \__\\ \__\
    \|__|\|__|    \|_______|    \|__|\|__|    \|__|\|__|        \|__|    \|__| \|__|

`)

	var rootCmd = &cobra.Command{
		Use:   "heartk [网站列表文件]",
		Short: "HeartK - 网站敏感信息扫描工具",
		Long:  `HeartK是一个网站敏感信息扫描工具，可以对指定的网站列表进行批量扫描并获取敏感信息。`,
		Args:  cobra.ExactArgs(1),
		RunE:  runScan,
	}

	rootCmd.Flags().BoolVarP(&verbose, "verbose", "d", false, "输出详细信息")
	rootCmd.Flags().StringVarP(&exportPath, "export", "e", "", "指定要导出报告的路径")

	if err := rootCmd.Execute(); err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
}

func runScan(cmd *cobra.Command, args []string) error {
	listPath := args[0]

	// 加载配置
	cfg := config.LoadConfig()

	// 检查网站列表文件是否存在
	if _, err := os.Stat(listPath); err != nil {
		return fmt.Errorf("网站列表文件不存在: %v", err)
	}

	// 扫描网站列表
	result, _, err := scanWebsiteList(listPath, cfg)
	if err != nil {
		return err
	}

	// 由于网站扫描已经为每个网站生成了单独的报告，这里不需要再生成报告
	// 只在verbose模式下显示总体统计信息
	if verbose && result != nil {
		fmt.Printf("\033[31m网站扫描完成\033[0m\n")
	}

	return nil
}



func scanWebsiteList(listPath string, cfg *types.ScanConfig) (*types.ScanResult, string, error) {
	// 读取网站列表
	websites, err := readWebsiteList(listPath)
	if err != nil {
		return nil, "", fmt.Errorf("failed to read website list: %v", err)
	}
	
	if len(websites) == 0 {
		return nil, "", fmt.Errorf("%s未读取到有效数据", listPath)
	}
	
	// 创建网站扫描器
	webScanner, err := scanner.NewWebScanner(cfg)
	if err != nil {
		return nil, "", fmt.Errorf("failed to create web scanner: %v", err)
	}
	defer webScanner.Close()
	
	// 创建报告生成器
	htmlReporter := reporter.NewHTMLReporter()
	
	// 逐个扫描网站
	for _, website := range websites {
		result, err := webScanner.ScanWebsite(website, verbose)
		if err != nil {
			fmt.Printf("Warning: failed to scan %s: %v\n", website, err)
			continue
		}
		
		// 为每个网站生成单独的报告
		hostname := webScanner.GetHostname(website)
		err = htmlReporter.GenerateReport(result, exportPath, hostname)
		if err != nil {
			fmt.Printf("Warning: failed to generate report for %s: %v\n", website, err)
		}
	}
	
	// 返回空结果，因为已经为每个网站生成了单独的报告
	return types.NewScanResult(), "", nil
}

func readWebsiteList(filePath string) ([]string, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return nil, err
	}
	defer file.Close()
	
	var websites []string
	scanner := bufio.NewScanner(file)
	
	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())
		if line != "" {
			websites = append(websites, line)
		}
	}
	
	if err := scanner.Err(); err != nil {
		return nil, err
	}
	
	return websites, nil
}
