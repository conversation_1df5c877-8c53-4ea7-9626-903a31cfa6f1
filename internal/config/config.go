package config

import (
	"heartk-go/pkg/types"
)

// DefaultConfig 默认配置，与原Python版本保持一致
func DefaultConfig() *types.ScanConfig {
	return &types.ScanConfig{
		WaitTime: 5, // 5秒超时
		Headers: map[string]string{
			"Accept":                    "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
			"Accept-Language":           "zh-CN,zh;q=0.9,en;q=0.8",
			"Cache-Control":             "no-cache",
			"Connection":                "keep-alive",
			"Pragma":                    "no-cache",
			"Upgrade-Insecure-Requests": "1",
			"User-Agent":                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/132.0.0.0 Safari/537.36",
		},
	}
}

// LoadConfig 加载配置
func LoadConfig() *types.ScanConfig {
	// 目前使用默认配置，后续可以扩展从文件加载
	return DefaultConfig()
}
