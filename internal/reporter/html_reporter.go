package reporter

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"

	"heartk-go/pkg/types"
)

// HTMLReporter HTML报告生成器
type HTMLReporter struct{}

// NewHTMLReporter 创建新的HTML报告生成器
func NewHTMLReporter() *HTMLReporter {
	return &HTMLReporter{}
}

// GenerateReport 生成HTML报告
func (hr *HTMLReporter) GenerateReport(result *types.ScanResult, exportPath string, hostname string) error {
	// 将结果转换为JSON
	jsonData, err := json.Marshal(result)
	if err != nil {
		return fmt.Errorf("failed to marshal result to JSON: %v", err)
	}
	
	// 生成HTML内容
	htmlContent := hr.generateHTMLContent(string(jsonData))
	
	// 确定输出文件路径
	var outputPath string
	if hostname != "" {
		// 网站扫描模式
		if exportPath != "" {
			if !hr.isDirectory(exportPath) {
				return fmt.Errorf("指定的导出报告路径并非有效路径")
			}
			outputPath = filepath.Join(exportPath, hostname+".html")
		} else {
			// 创建web_reports目录
			webReportsDir := "web_reports"
			if err := os.MkdirAll(webReportsDir, 0755); err != nil {
				return fmt.Errorf("failed to create web_reports directory: %v", err)
			}
			outputPath = filepath.Join(webReportsDir, hostname+".html")
		}
	} else {
		// 本地文件扫描模式
		if exportPath != "" {
			if !hr.isDirectory(exportPath) {
				return fmt.Errorf("指定的导出报告路径并非有效路径")
			}
			outputPath = filepath.Join(exportPath, "report.html")
		} else {
			outputPath = "report.html"
		}
	}
	
	// 写入文件
	err = hr.writeFile(outputPath, htmlContent)
	if err != nil {
		return fmt.Errorf("failed to write report file: %v", err)
	}
	
	// 打印输出信息
	if hostname != "" {
		fmt.Printf("\033[31m报告已导出在%s\033[0m\n", outputPath)
	} else {
		fmt.Printf("报告已导出在%s\n", outputPath)
	}
	
	return nil
}

// generateHTMLContent 生成HTML内容
func (hr *HTMLReporter) generateHTMLContent(jsonData string) string {
	popupJS := `
    // @Date    : 2020-09-12 16:26:48
    // <AUTHOR> residuallaugh

    var key = ["ip","ip_port","domain","path","incomplete_path","url","static","sfz","mobile","mail","jwt","algorithm","secret"]

    let messages = {
        "popupCopy": "复制",
        "popupCopyurl": "复制URL",
        "popupTipClickBeforeCopy": "请点击原页面后再复制：）"
    };

    function getMessage(key) {
        return messages[key] || "";
    }

    function init_copy() {
        var elements = document.getElementsByClassName("copy");
        if(elements){
            for (var i=0, len=elements.length|0; i<len; i=i+1|0) {
                elements[i].textContent = getMessage("popupCopy");
                let ele_name = elements[i].name;
                let ele_id = elements[i].id;
                if (ele_id == "popupCopyurl"){
                    elements[i].textContent = getMessage("popupCopyurl");
                }
                elements[i].onclick=function () {
                    var inp =document.createElement('textarea');
                    document.body.appendChild(inp)
                    var copytext = document.getElementById(ele_name).textContent;
                    inp.value = copytext;
                    inp.select();
                    document.execCommand('copy',false);
                    inp.remove();
                }
            }
        }
    };

    function show_info(result_data) {
        for (var k in key){
            if (result_data[key[k]]){
                let container = document.getElementById(key[k]);
                while((ele = container.firstChild)){
                    ele.remove();
                }
                container.style.whiteSpace = "pre";
                for (var i in result_data[key[k]]){
                    let tips = document.createElement("div");
                    tips.setAttribute("class", "tips")
                    let link = document.createElement("a");
                    link.appendChild(tips);
                    let span = document.createElement("span");
                    span.textContent = result_data[key[k]][i]+'\n';
                    container.appendChild(link);
                    container.appendChild(span);
                }
            }
        }
    }

    init_copy();

    show_info(` + jsonData + `)`

	htmlTemplate := `<!--  
@Date    : 2020-09-12 16:26:48
<AUTHOR> residuallaugh 
-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
</head>
<body style="width:780px; font-size: 14px;">
    <div style="width:780px; height: 30px; margin-left: 15px;">
        <a href="report.html"><div id="Zhuye" style="width: 55px; height: 28px; float: left; text-align: center; line-height: 28px; font-size: 14px; background: #000000; color: #ffffff; border: 1px solid black;border-radius: 2px 0px 0px 2px;">主页</div></a>
    </div>
    <div style="width:780px; height: 800px; margin-left: 15px;">
        <div id="taskstatus" style="height: 34px; line-height: 34px;"></div>
        <div style="width: 256px; float: left; border-right: 1px solid #e8e8e8;">
            <div class="findsomething_title" id="popupIp">IP</div><button type="button" class="copy" name="ip">复制</button>
            <p id="ip" style="word-break:break-word;">🈚️</p>
            <div class="findsomething_title" id="popupIpPort">IP_PORT</div><button class="copy" name="ip_port">复制</button>
            <p id="ip_port" style="word-break:break-word;">🈚️</p>
            <div class="findsomething_title" id="popupDomain">域名</div><button class="copy" name="domain">复制</button>
            <p id="domain" style="word-break:break-word;">🈚️</p>
            <div class="findsomething_title" id="popupSfz">身份证</div><button class="copy" name="sfz">复制</button>
            <p id="sfz" style="">🈚️</p>
            <div class="findsomething_title" id="popupMobile">手机号</div><button class="copy" name="mobile">复制</button>
            <p id="mobile" style="">🈚️</p>
            <div class="findsomething_title" id="popupMail">邮箱</div><button class="copy" name="mail">复制</button>
            <p id="mail" style="">🈚️</p>
            <div class="findsomething_title" id="popupJwt">JWT<button class="copy" name="jwt">复制</button></div>
            <p id="jwt" style="word-break:break-word;">🈚️</p>
            <div class="findsomething_title" id="popupAlgorithm">算法</div><button class="copy" name="algorithm">复制</button>
            <p id="algorithm" style="">🈚️</p>
            <div class="findsomething_title" id="popupSecret">Secret</div><button class="copy" name="secret">复制</button>
            <p id="secret" style="">🈚️</p>
        </div>
        <div style="width: 480px; height: 800px; float: left; margin-left:16px;">
            <div class="findsomething_title" id="popupPath">Path</div><button id="path_button" class="copy" name="path">复制</button>
            <p id="path" style="">🈚️</p>
            <div class="findsomething_title" id="popupIncompletePath">IncompletePath</div><button class="copy" name="incomplete_path">复制</button>
            <p id="incomplete_path" style="">🈚️</p>
            <div class="findsomething_title" id="popupUrl">Url</div><button class="copy" name="url">复制</button>
            <p id="url" style="">🈚️</p>
            <div class="findsomething_title" id="popupStaticPath">StaticUrl</div><button class="copy" name="static">复制</button>
            <p id="static" style="">🈚️</p>
        </div>
</div>
</body>
<script>
    ` + popupJS + `
</script>
<style type="text/css">
    .copy {
        border-style: none;
        background-color: #ffffff;
        float: right;
        margin-right: 16px;
        
    }
    .findsomething_title {
        font-size: 16px;
        font-weight: bold;
        border-left: 4px solid black;
        text-indent: 4px;
        height: 16px;
        line-height: 16px;
    }
    .tips {
        display: inline-block;
        border-top: 0.2px solid;
        border-right: 0.2px solid;
        width: 10px;
        height: 10px;
        border-color: #EA6000;
        transform: rotate(-135deg);
    }
    a{
        text-decoration:none;
        color:#333;
    }
    button{
        cursor: pointer
    }
</style>
</html>`

	return htmlTemplate
}

// isDirectory 检查路径是否为目录
func (hr *HTMLReporter) isDirectory(path string) bool {
	info, err := os.Stat(path)
	if err != nil {
		return false
	}
	return info.IsDir()
}

// writeFile 写入文件
func (hr *HTMLReporter) writeFile(path, content string) error {
	return os.WriteFile(path, []byte(content), 0644)
}
