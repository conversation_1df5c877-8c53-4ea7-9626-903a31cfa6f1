package scanner

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"path/filepath"

	"heartk-go/pkg/types"

	"github.com/dop251/goja"
)

// JSEngine JavaScript引擎封装
type JSEngine struct {
	vm     *goja.Runtime
	getInfo goja.Callable
}

// NewJSEngine 创建新的JavaScript引擎
func NewJSEngine() (*JSEngine, error) {
	vm := goja.New()
	
	// 读取JavaScript文件
	jsPath := filepath.Join("assets", "background.js")
	jsContent, err := ioutil.ReadFile(jsPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read JavaScript file: %v", err)
	}
	
	// 执行JavaScript代码
	_, err = vm.RunString(string(jsContent))
	if err != nil {
		return nil, fmt.Errorf("failed to execute JavaScript: %v", err)
	}
	
	// 获取get_info函数
	getInfoValue := vm.Get("get_info")
	if getInfoValue == nil {
		return nil, fmt.Errorf("get_info function not found in JavaScript")
	}
	
	getInfo, ok := goja.AssertFunction(getInfoValue)
	if !ok {
		return nil, fmt.Errorf("get_info is not a function")
	}
	
	return &JSEngine{
		vm:      vm,
		getInfo: getInfo,
	}, nil
}

// ExtractInfo 从文本中提取敏感信息
func (js *JSEngine) ExtractInfo(content string) (*types.ScanResult, error) {
	// 调用JavaScript的get_info函数
	result, err := js.getInfo(goja.Undefined(), js.vm.ToValue(content))
	if err != nil {
		return nil, fmt.Errorf("failed to call get_info: %v", err)
	}

	// 将JavaScript结果导出为Go值
	exportedResult := result.Export()

	// 将Go值转换为JSON，然后再解析为结构体
	jsonBytes, err := json.Marshal(exportedResult)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal JavaScript result: %v", err)
	}

	// 解析JSON到Go结构体
	var scanResult types.ScanResult
	err = json.Unmarshal(jsonBytes, &scanResult)
	if err != nil {
		return nil, fmt.Errorf("failed to parse JavaScript result: %v", err)
	}

	return &scanResult, nil
}

// Close 关闭JavaScript引擎
func (js *JSEngine) Close() {
	// goja不需要显式关闭
}
