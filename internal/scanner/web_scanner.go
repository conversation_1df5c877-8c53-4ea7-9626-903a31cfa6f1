package scanner

import (
	"crypto/tls"
	"fmt"
	"io/ioutil"
	"net/http"
	"net/url"
	"strings"
	"time"

	"heartk-go/pkg/types"

	"github.com/PuerkitoBio/goquery"
)

// WebScanner 网站扫描器
type WebScanner struct {
	jsEngine *JSEngine
	config   *types.ScanConfig
	client   *http.Client
}

// NewWebScanner 创建新的网站扫描器
func NewWebScanner(config *types.ScanConfig) (*WebScanner, error) {
	jsEngine, err := NewJSEngine()
	if err != nil {
		return nil, fmt.Errorf("failed to create JS engine: %v", err)
	}
	
	// 创建HTTP客户端，禁用SSL验证
	client := &http.Client{
		Timeout: time.Duration(config.WaitTime) * time.Second,
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
		},
	}
	
	return &WebScanner{
		jsEngine: jsEngine,
		config:   config,
		client:   client,
	}, nil
}

// ScanWebsite 扫描单个网站
func (ws *WebScanner) ScanWebsite(websiteURL string, verbose bool) (*types.ScanResult, error) {
	// 获取网站的JS文件和内联JS代码
	jsFiles, jsCode, err := ws.getHostJS(websiteURL)
	if err != nil {
		return nil, fmt.Errorf("failed to get JS from %s: %v", websiteURL, err)
	}
	
	allResult := types.NewScanResult()
	
	// 扫描外部JS文件
	for _, jsURL := range jsFiles {
		result, err := ws.scanJSURL(jsURL)
		if err != nil {
			fmt.Printf("Warning: failed to scan JS file %s: %v\n", jsURL, err)
			continue
		}
		allResult.Merge(result)
	}
	
	// 扫描内联JS代码
	for _, code := range jsCode {
		result, err := ws.jsEngine.ExtractInfo(code)
		if err != nil {
			fmt.Printf("Warning: failed to extract info from inline JS: %v\n", err)
			continue
		}
		allResult.Merge(result)
	}
	
	// 去重和排序
	allResult.Deduplicate()
	
	// 如果启用详细输出，打印统计信息
	if verbose {
		fmt.Println(allResult.PrintSummary(websiteURL))
	}
	
	return allResult, nil
}

// ScanWebsites 批量扫描网站
func (ws *WebScanner) ScanWebsites(urls []string, verbose bool) ([]*types.ScanResult, error) {
	var results []*types.ScanResult
	
	for _, url := range urls {
		result, err := ws.ScanWebsite(url, verbose)
		if err != nil {
			fmt.Printf("Warning: failed to scan website %s: %v\n", url, err)
			continue
		}
		results = append(results, result)
	}
	
	return results, nil
}

// getHostJS 获取网站的JS文件和内联JS代码
func (ws *WebScanner) getHostJS(websiteURL string) ([]string, []string, error) {
	// 创建HTTP请求
	req, err := http.NewRequest("GET", websiteURL, nil)
	if err != nil {
		return nil, nil, err
	}
	
	// 设置请求头
	for key, value := range ws.config.Headers {
		req.Header.Set(key, value)
	}
	
	// 发送请求
	resp, err := ws.client.Do(req)
	if err != nil {
		return nil, nil, err
	}
	defer resp.Body.Close()
	
	// 读取响应内容
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, nil, err
	}
	
	// 解析HTML
	doc, err := goquery.NewDocumentFromReader(strings.NewReader(string(body)))
	if err != nil {
		return nil, nil, err
	}
	
	var jsFiles []string
	var jsCode []string
	
	// 解析URL
	baseURL, err := url.Parse(websiteURL)
	if err != nil {
		return nil, nil, err
	}
	
	// 提取script标签中的src属性
	doc.Find("script").Each(func(i int, s *goquery.Selection) {
		if src, exists := s.Attr("src"); exists {
			// 处理相对URL
			absoluteURL := ws.resolveURL(baseURL, src)
			jsFiles = append(jsFiles, absoluteURL)
		} else {
			// 内联JavaScript代码
			code := strings.TrimSpace(s.Text())
			if code != "" {
				jsCode = append(jsCode, code)
			}
		}
	})
	
	// 提取link标签中的JS文件
	doc.Find("link").Each(func(i int, s *goquery.Selection) {
		if href, exists := s.Attr("href"); exists && strings.Contains(href, ".js") {
			absoluteURL := ws.resolveURL(baseURL, href)
			jsFiles = append(jsFiles, absoluteURL)
		}
	})
	
	// 将页面HTML内容也加入扫描
	jsCode = append(jsCode, string(body))
	
	return jsFiles, jsCode, nil
}

// scanJSURL 扫描JS文件URL
func (ws *WebScanner) scanJSURL(jsURL string) (*types.ScanResult, error) {
	// 创建HTTP请求
	req, err := http.NewRequest("GET", jsURL, nil)
	if err != nil {
		return nil, err
	}
	
	// 设置请求头
	for key, value := range ws.config.Headers {
		req.Header.Set(key, value)
	}
	
	// 发送请求
	resp, err := ws.client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	
	// 读取JS文件内容
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	
	// 使用JavaScript引擎提取信息
	return ws.jsEngine.ExtractInfo(string(body))
}

// resolveURL 解析相对URL为绝对URL
func (ws *WebScanner) resolveURL(baseURL *url.URL, href string) string {
	if strings.HasPrefix(href, "//") {
		return baseURL.Scheme + ":" + href
	} else if strings.HasPrefix(href, "http://") || strings.HasPrefix(href, "https://") {
		return href
	} else {
		// 相对URL
		if strings.HasPrefix(href, "/") {
			return baseURL.Scheme + "://" + baseURL.Host + href
		} else {
			return baseURL.Scheme + "://" + baseURL.Host + "/" + href
		}
	}
}

// GetHostname 从URL中提取主机名
func (ws *WebScanner) GetHostname(websiteURL string) string {
	parsedURL, err := url.Parse(websiteURL)
	if err != nil {
		return "unknown"
	}
	
	hostname := parsedURL.Hostname()
	if parsedURL.Port() != "" {
		hostname += "_" + parsedURL.Port()
	}
	
	return hostname
}

// Close 关闭网站扫描器
func (ws *WebScanner) Close() {
	if ws.jsEngine != nil {
		ws.jsEngine.Close()
	}
}
