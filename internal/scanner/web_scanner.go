package scanner

import (
	"crypto/tls"
	"fmt"
	"io/ioutil"
	"net/http"
	"net/url"
	"regexp"
	"strings"
	"time"

	"heartk-go/pkg/types"

	"github.com/PuerkitoBio/goquery"
)

// WebScanner 网站扫描器
type WebScanner struct {
	jsEngine *JSEngine
	config   *types.ScanConfig
	client   *http.Client
}

// NewWebScanner 创建新的网站扫描器
func NewWebScanner(config *types.ScanConfig) (*WebScanner, error) {
	jsEngine, err := NewJSEngine()
	if err != nil {
		return nil, fmt.Errorf("failed to create JS engine: %v", err)
	}
	
	// 创建HTTP客户端，禁用SSL验证
	client := &http.Client{
		Timeout: time.Duration(config.WaitTime) * time.Second,
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
		},
	}
	
	return &WebScanner{
		jsEngine: jsEngine,
		config:   config,
		client:   client,
	}, nil
}

// ScanWebsite 扫描单个网站
func (ws *WebScanner) ScanWebsite(websiteURL string, verbose bool) (*types.ScanResult, error) {
	// 获取网站的JS文件和内联JS代码
	jsFiles, jsCode, err := ws.getHostJS(websiteURL)
	if err != nil {
		return nil, fmt.Errorf("failed to get JS from %s: %v", websiteURL, err)
	}
	
	allResult := types.NewScanResult()
	
	// 扫描外部JS文件
	for _, jsURL := range jsFiles {
		result, err := ws.scanJSURL(jsURL)
		if err != nil {
			fmt.Printf("Warning: failed to scan JS file %s: %v\n", jsURL, err)
			continue
		}
		allResult.Merge(result)
	}
	
	// 扫描内联JS代码
	for _, code := range jsCode {
		result, err := ws.jsEngine.ExtractInfo(code)
		if err != nil {
			fmt.Printf("Warning: failed to extract info from inline JS: %v\n", err)
			continue
		}
		allResult.Merge(result)
	}
	
	// 去重和排序
	allResult.Deduplicate()
	
	// 如果启用详细输出，打印统计信息
	if verbose {
		fmt.Println(allResult.PrintSummary(websiteURL))
	}
	
	return allResult, nil
}

// ScanWebsites 批量扫描网站
func (ws *WebScanner) ScanWebsites(urls []string, verbose bool) ([]*types.ScanResult, error) {
	var results []*types.ScanResult
	
	for _, url := range urls {
		result, err := ws.ScanWebsite(url, verbose)
		if err != nil {
			fmt.Printf("Warning: failed to scan website %s: %v\n", url, err)
			continue
		}
		results = append(results, result)
	}
	
	return results, nil
}

// getHostJS 获取网站的JS文件和内联JS代码，使用与原版FindSomething一致的逻辑
func (ws *WebScanner) getHostJS(websiteURL string) ([]string, []string, error) {
	// 检查白名单
	if ws.isInAllowList(websiteURL) {
		return nil, nil, fmt.Errorf("域名在白名单中，跳过当前页")
	}

	// 创建HTTP请求
	req, err := http.NewRequest("GET", websiteURL, nil)
	if err != nil {
		return nil, nil, err
	}

	// 设置请求头
	for key, value := range ws.config.Headers {
		req.Header.Set(key, value)
	}

	// 发送请求
	resp, err := ws.client.Do(req)
	if err != nil {
		return nil, nil, err
	}
	defer resp.Body.Close()

	// 读取响应内容
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, nil, err
	}

	source := string(body)
	var jsFiles []string
	var jsCode []string

	// 解析URL
	baseURL, err := url.Parse(websiteURL)
	if err != nil {
		return nil, nil, err
	}

	// 使用正则表达式提取，与原版FindSomething保持一致
	targetList := ws.extractURLsFromSource(source, baseURL)

	// 过滤JS文件
	for _, targetURL := range targetList {
		if ws.config.SafeMode {
			if ws.isJavaScriptFile(targetURL) || ws.isScriptSrc(source, targetURL) {
				jsFiles = append(jsFiles, targetURL)
			}
		} else {
			jsFiles = append(jsFiles, targetURL)
		}
	}

	// 提取内联JavaScript代码
	doc, err := goquery.NewDocumentFromReader(strings.NewReader(source))
	if err == nil {
		doc.Find("script").Each(func(i int, s *goquery.Selection) {
			if _, exists := s.Attr("src"); !exists {
				// 内联JavaScript代码
				code := strings.TrimSpace(s.Text())
				if code != "" {
					jsCode = append(jsCode, code)
				}
			}
		})
	}

	// 将页面HTML内容也加入扫描
	jsCode = append(jsCode, source)

	return jsFiles, jsCode, nil
}

// scanJSURL 扫描JS文件URL
func (ws *WebScanner) scanJSURL(jsURL string) (*types.ScanResult, error) {
	// 创建HTTP请求
	req, err := http.NewRequest("GET", jsURL, nil)
	if err != nil {
		return nil, err
	}
	
	// 设置请求头
	for key, value := range ws.config.Headers {
		req.Header.Set(key, value)
	}
	
	// 发送请求
	resp, err := ws.client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	
	// 读取JS文件内容
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	
	// 使用JavaScript引擎提取信息
	return ws.jsEngine.ExtractInfo(string(body))
}

// resolveURL 解析相对URL为绝对URL
func (ws *WebScanner) resolveURL(baseURL *url.URL, href string) string {
	if strings.HasPrefix(href, "//") {
		return baseURL.Scheme + ":" + href
	} else if strings.HasPrefix(href, "http://") || strings.HasPrefix(href, "https://") {
		return href
	} else {
		// 相对URL
		if strings.HasPrefix(href, "/") {
			return baseURL.Scheme + "://" + baseURL.Host + href
		} else {
			return baseURL.Scheme + "://" + baseURL.Host + "/" + href
		}
	}
}

// GetHostname 从URL中提取主机名
func (ws *WebScanner) GetHostname(websiteURL string) string {
	parsedURL, err := url.Parse(websiteURL)
	if err != nil {
		return "unknown"
	}
	
	hostname := parsedURL.Hostname()
	if parsedURL.Port() != "" {
		hostname += "_" + parsedURL.Port()
	}
	
	return hostname
}

// isInAllowList 检查域名是否在白名单中
func (ws *WebScanner) isInAllowList(websiteURL string) bool {
	parsedURL, err := url.Parse(websiteURL)
	if err != nil {
		return false
	}

	host := parsedURL.Host
	domainHost := strings.Split(host, ":")[0]

	for _, allowDomain := range ws.config.AllowList {
		if strings.HasSuffix(host, allowDomain) || strings.HasSuffix(domainHost, allowDomain) {
			return true
		}
	}
	return false
}

// extractURLsFromSource 从源码中提取URL，与原版FindSomething逻辑一致
func (ws *WebScanner) extractURLsFromSource(source string, baseURL *url.URL) []string {
	var targetList []string

	// 使用正则表达式提取href属性
	hrefRegex := regexp.MustCompile(`href=['"].*?['"]`)
	hrefMatches := hrefRegex.FindAllString(source, -1)
	for _, match := range hrefMatches {
		u := match[6 : len(match)-1] // 去掉 href=" 和 "
		absoluteURL := ws.dealURL(u, baseURL)
		if absoluteURL != "" {
			targetList = append(targetList, absoluteURL)
		}
	}

	// 使用正则表达式提取src属性
	srcRegex := regexp.MustCompile(`src=['"].*?['"]`)
	srcMatches := srcRegex.FindAllString(source, -1)
	for _, match := range srcMatches {
		u := match[5 : len(match)-1] // 去掉 src=" 和 "
		absoluteURL := ws.dealURL(u, baseURL)
		if absoluteURL != "" {
			targetList = append(targetList, absoluteURL)
		}
	}

	// 使用正则表达式提取script标签的src
	scriptRegex := regexp.MustCompile(`<script [^><]*?src=['"].*?['"]`)
	scriptMatches := scriptRegex.FindAllString(source, -1)
	for _, match := range scriptMatches {
		srcMatch := regexp.MustCompile(`src=['"]([^'"]*?)['"]`).FindStringSubmatch(match)
		if len(srcMatch) > 1 {
			absoluteURL := ws.dealURL(srcMatch[1], baseURL)
			if absoluteURL != "" {
				targetList = append(targetList, absoluteURL)
			}
		}
	}

	// 去重
	uniqueList := make([]string, 0)
	seen := make(map[string]bool)
	for _, item := range targetList {
		if !seen[item] {
			seen[item] = true
			uniqueList = append(uniqueList, item)
		}
	}

	return uniqueList
}

// dealURL 处理URL，与原版FindSomething的deal_url函数逻辑一致
func (ws *WebScanner) dealURL(u string, baseURL *url.URL) string {
	var targetURL string

	if strings.HasPrefix(u, "http") {
		targetURL = u
	} else if strings.HasPrefix(u, "//") {
		targetURL = baseURL.Scheme + ":" + u
	} else if strings.HasPrefix(u, "/") {
		targetURL = baseURL.Scheme + "://" + baseURL.Host + u
	} else if strings.HasPrefix(u, "./") {
		basePath := baseURL.Path
		if strings.Contains(baseURL.String(), "#") {
			basePath = strings.Split(baseURL.String(), "#")[0]
		}
		if strings.LastIndex(basePath, "/") >= 0 {
			targetURL = baseURL.Scheme + "://" + baseURL.Host + basePath[:strings.LastIndex(basePath, "/")+1] + u
		} else {
			targetURL = baseURL.Scheme + "://" + baseURL.Host + "/" + u
		}
	} else {
		// 相对路径
		basePath := baseURL.Path
		if strings.Contains(baseURL.String(), "#") {
			basePath = strings.Split(baseURL.String(), "#")[0]
		}
		if strings.LastIndex(basePath, "/") >= 0 {
			targetURL = baseURL.Scheme + "://" + baseURL.Host + basePath[:strings.LastIndex(basePath, "/")+1] + u
		} else {
			targetURL = baseURL.Scheme + "://" + baseURL.Host + "/" + u
		}
	}

	// 安全模式检查
	if ws.config.SafeMode && !ws.isJavaScriptFile(targetURL) && !ws.isScriptSrc("", u) {
		return ""
	}

	return targetURL
}

// isJavaScriptFile 检查是否为JavaScript文件
func (ws *WebScanner) isJavaScriptFile(urlStr string) bool {
	parsedURL, err := url.Parse(urlStr)
	if err != nil {
		return false
	}
	return strings.HasSuffix(parsedURL.Path, ".js")
}

// isScriptSrc 检查是否在script标签的src中
func (ws *WebScanner) isScriptSrc(source, u string) bool {
	if source == "" {
		return false
	}
	scriptRegex := regexp.MustCompile(`<script [^><]*?src=['"].*?['"]`)
	scriptMatches := scriptRegex.FindAllString(source, -1)
	for _, match := range scriptMatches {
		if strings.Contains(match, u) {
			return true
		}
	}
	return false
}

// Close 关闭网站扫描器
func (ws *WebScanner) Close() {
	if ws.jsEngine != nil {
		ws.jsEngine.Close()
	}
}
