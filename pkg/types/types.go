package types

import (
	"fmt"
	"time"
)

// ScanResult 扫描结果数据结构，与JavaScript返回的JSON结构保持一致
type ScanResult struct {
	SFZ            []string `json:"sfz"`             // 身份证号
	Mobile         []string `json:"mobile"`          // 手机号
	Mail           []string `json:"mail"`            // 邮箱
	IP             []string `json:"ip"`              // IP地址
	IPPort         []string `json:"ip_port"`         // IP:端口
	Domain         []string `json:"domain"`          // 域名
	Path           []string `json:"path"`            // 路径
	IncompletePath []string `json:"incomplete_path"` // 不完整路径
	URL            []string `json:"url"`             // URL
	JWT            []string `json:"jwt"`             // JWT令牌
	Algorithm      []string `json:"algorithm"`       // 算法
	Secret         []string `json:"secret"`          // 密钥
	Static         []string `json:"static"`          // 静态文件
}

// ScanConfig 扫描配置
type ScanConfig struct {
	WaitTime       int               `json:"wait_time"`        // 请求超时时间(秒)
	Headers        map[string]string `json:"headers"`          // HTTP请求头
	SafeMode       bool              `json:"safe_mode"`        // 安全模式，只扫描JS文件
	AllowList      []string          `json:"allow_list"`       // 白名单域名
	FetchTimeout   bool              `json:"fetch_timeout"`    // 是否启用请求超时
}

// ScanOptions 扫描选项
type ScanOptions struct {
	Path       string // 扫描路径
	Verbose    bool   // 详细输出
	ExportPath string // 导出路径
}

// FileInfo 文件信息
type FileInfo struct {
	Path     string
	Content  string
	Encoding string
}

// WebInfo 网站信息
type WebInfo struct {
	URL     string
	JSFiles []string
	JSCode  []string
}

// ScanStats 扫描统计
type ScanStats struct {
	StartTime    time.Time
	EndTime      time.Time
	FilesScanned int
	URLsScanned  int
	TotalItems   int
}

// NewScanResult 创建新的扫描结果
func NewScanResult() *ScanResult {
	return &ScanResult{
		SFZ:            make([]string, 0),
		Mobile:         make([]string, 0),
		Mail:           make([]string, 0),
		IP:             make([]string, 0),
		IPPort:         make([]string, 0),
		Domain:         make([]string, 0),
		Path:           make([]string, 0),
		IncompletePath: make([]string, 0),
		URL:            make([]string, 0),
		JWT:            make([]string, 0),
		Algorithm:      make([]string, 0),
		Secret:         make([]string, 0),
		Static:         make([]string, 0),
	}
}

// Merge 合并扫描结果
func (sr *ScanResult) Merge(other *ScanResult) {
	if other == nil {
		return
	}
	sr.SFZ = append(sr.SFZ, other.SFZ...)
	sr.Mobile = append(sr.Mobile, other.Mobile...)
	sr.Mail = append(sr.Mail, other.Mail...)
	sr.IP = append(sr.IP, other.IP...)
	sr.IPPort = append(sr.IPPort, other.IPPort...)
	sr.Domain = append(sr.Domain, other.Domain...)
	sr.Path = append(sr.Path, other.Path...)
	sr.IncompletePath = append(sr.IncompletePath, other.IncompletePath...)
	sr.URL = append(sr.URL, other.URL...)
	sr.JWT = append(sr.JWT, other.JWT...)
	sr.Algorithm = append(sr.Algorithm, other.Algorithm...)
	sr.Secret = append(sr.Secret, other.Secret...)
	sr.Static = append(sr.Static, other.Static...)
}

// Deduplicate 去重并排序
func (sr *ScanResult) Deduplicate() {
	sr.SFZ = deduplicateSlice(sr.SFZ)
	sr.Mobile = deduplicateSlice(sr.Mobile)
	sr.Mail = deduplicateSlice(sr.Mail)
	sr.IP = deduplicateSlice(sr.IP)
	sr.IPPort = deduplicateSlice(sr.IPPort)
	sr.Domain = deduplicateSlice(sr.Domain)
	sr.Path = deduplicateSlice(sr.Path)
	sr.IncompletePath = deduplicateSlice(sr.IncompletePath)
	sr.URL = deduplicateSlice(sr.URL)
	sr.JWT = deduplicateSlice(sr.JWT)
	sr.Algorithm = deduplicateSlice(sr.Algorithm)
	sr.Secret = deduplicateSlice(sr.Secret)
	sr.Static = deduplicateSlice(sr.Static)
}

// Count 统计总数
func (sr *ScanResult) Count() int {
	return len(sr.SFZ) + len(sr.Mobile) + len(sr.Mail) + len(sr.IP) +
		len(sr.IPPort) + len(sr.Domain) + len(sr.Path) + len(sr.IncompletePath) +
		len(sr.URL) + len(sr.JWT) + len(sr.Algorithm) + len(sr.Secret) + len(sr.Static)
}

// PrintSummary 打印统计摘要
func (sr *ScanResult) PrintSummary(source string) string {
	return fmt.Sprintf("%s共搜索到：%d个sfz\t%d个mobile\t%d个mail\t%d个ip\t%d个ip_port\t%d个domain\t%d个path\t%d个incomplete_path\t%d个url\t%d个jwt\t%d个algorithm\t%d个secret\t%d个static",
		source,
		len(sr.SFZ), len(sr.Mobile), len(sr.Mail), len(sr.IP), len(sr.IPPort),
		len(sr.Domain), len(sr.Path), len(sr.IncompletePath), len(sr.URL),
		len(sr.JWT), len(sr.Algorithm), len(sr.Secret), len(sr.Static))
}

// deduplicateSlice 去重切片
func deduplicateSlice(slice []string) []string {
	if len(slice) == 0 {
		return slice
	}
	
	seen := make(map[string]bool)
	result := make([]string, 0, len(slice))
	
	for _, item := range slice {
		if item != "" && !seen[item] {
			seen[item] = true
			result = append(result, item)
		}
	}
	
	return result
}
