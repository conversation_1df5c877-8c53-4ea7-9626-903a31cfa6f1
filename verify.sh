#!/bin/bash

# HeartK-Go 项目验证脚本

echo "=== HeartK-Go 项目验证 ==="
echo

# 检查项目结构
echo "1. 检查项目结构..."
required_files=(
    "go.mod"
    "cmd/heartk/main.go"
    "pkg/types/types.go"
    "internal/config/config.go"
    "internal/scanner/js_engine.go"
    "internal/scanner/web_scanner.go"
    "internal/reporter/html_reporter.go"
    "assets/background.js"
    "README.md"
    "MIGRATION_GUIDE.md"
    "build.sh"
    "build.bat"
    "example_websites.txt"
)

missing_files=()
for file in "${required_files[@]}"; do
    if [ ! -f "$file" ]; then
        missing_files+=("$file")
    fi
done

if [ ${#missing_files[@]} -eq 0 ]; then
    echo "✅ 所有必需文件都存在"
else
    echo "❌ 缺少以下文件:"
    for file in "${missing_files[@]}"; do
        echo "   - $file"
    done
fi

echo

# 检查Go文件语法
echo "2. 检查Go文件语法..."
if command -v go &> /dev/null; then
    if go mod verify &> /dev/null; then
        echo "✅ go.mod 验证通过"
    else
        echo "⚠️  go.mod 验证失败，可能需要运行 go mod tidy"
    fi
    
    # 检查语法错误
    if go vet ./... &> /dev/null; then
        echo "✅ Go代码语法检查通过"
    else
        echo "❌ Go代码存在语法错误:"
        go vet ./...
    fi
else
    echo "⚠️  未找到Go环境，跳过语法检查"
fi

echo

# 检查JavaScript文件
echo "3. 检查JavaScript文件..."
if [ -f "assets/background.js" ]; then
    js_lines=$(wc -l < "assets/background.js")
    if [ "$js_lines" -gt 1000 ]; then
        echo "✅ background.js 文件完整 ($js_lines 行)"
    else
        echo "⚠️  background.js 文件可能不完整 ($js_lines 行)"
    fi
    
    # 检查关键函数
    if grep -q "function get_info" "assets/background.js"; then
        echo "✅ 找到 get_info 函数"
    else
        echo "❌ 未找到 get_info 函数"
    fi
else
    echo "❌ 未找到 background.js 文件"
fi

echo

# 检查文档
echo "4. 检查文档..."
docs=("README.md" "MIGRATION_GUIDE.md")
for doc in "${docs[@]}"; do
    if [ -f "$doc" ]; then
        lines=$(wc -l < "$doc")
        echo "✅ $doc 存在 ($lines 行)"
    else
        echo "❌ $doc 不存在"
    fi
done

echo

# 检查构建脚本
echo "5. 检查构建脚本..."
if [ -x "build.sh" ]; then
    echo "✅ build.sh 可执行"
else
    echo "⚠️  build.sh 不可执行，运行: chmod +x build.sh"
fi

if [ -f "build.bat" ]; then
    echo "✅ build.bat 存在"
else
    echo "❌ build.bat 不存在"
fi

echo

# 项目统计
echo "6. 项目统计..."
echo "Go文件数量: $(find . -name "*.go" | wc -l)"
echo "总代码行数: $(find . -name "*.go" -exec wc -l {} + | tail -1 | awk '{print $1}')"
echo "JavaScript行数: $(wc -l < assets/background.js)"
echo "文档行数: $(cat README.md MIGRATION_GUIDE.md | wc -l)"

echo

# 功能检查
echo "7. 功能模块检查..."
modules=(
    "types.ScanResult:pkg/types/types.go"
    "config.DefaultConfig:internal/config/config.go"
    "scanner.JSEngine:internal/scanner/js_engine.go"
    "scanner.WebScanner:internal/scanner/web_scanner.go"
    "reporter.HTMLReporter:internal/reporter/html_reporter.go"
)

for module in "${modules[@]}"; do
    name=$(echo "$module" | cut -d: -f1)
    file=$(echo "$module" | cut -d: -f2)
    
    if grep -q "$name" "$file" 2>/dev/null; then
        echo "✅ $name 模块存在"
    else
        echo "❌ $name 模块缺失"
    fi
done

echo

echo "=== 验证完成 ==="
echo
echo "如果所有检查都通过，可以尝试构建项目:"
echo "  ./build.sh        # Linux/macOS"
echo "  build.bat         # Windows"
echo
echo "构建成功后的使用方法:"
echo "  ./heartk [路径]   # 基本扫描"
echo "  ./heartk [路径] -d # 详细输出"
