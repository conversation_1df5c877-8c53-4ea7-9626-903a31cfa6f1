<!--  
@Date    : 2020-09-12 16:26:48
<AUTHOR> residuallaugh 
-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
</head>
<body style="width:780px; font-size: 14px;">
    <div style="width:780px; height: 30px; margin-left: 15px;">
        <a href="report.html"><div id="Zhuye" style="width: 55px; height: 28px; float: left; text-align: center; line-height: 28px; font-size: 14px; background: #000000; color: #ffffff; border: 1px solid black;border-radius: 2px 0px 0px 2px;">主页</div></a>
    </div>
    <div style="width:780px; height: 800px; margin-left: 15px;">
        <div id="taskstatus" style="height: 34px; line-height: 34px;"></div>
        <div style="width: 256px; float: left; border-right: 1px solid #e8e8e8;">
            <div class="findsomething_title" id="popupIp">IP</div><button type="button" class="copy" name="ip">复制</button>
            <p id="ip" style="word-break:break-word;">🈚️</p>
            <div class="findsomething_title" id="popupIpPort">IP_PORT</div><button class="copy" name="ip_port">复制</button>
            <p id="ip_port" style="word-break:break-word;">🈚️</p>
            <div class="findsomething_title" id="popupDomain">域名</div><button class="copy" name="domain">复制</button>
            <p id="domain" style="word-break:break-word;">🈚️</p>
            <div class="findsomething_title" id="popupSfz">身份证</div><button class="copy" name="sfz">复制</button>
            <p id="sfz" style="">🈚️</p>
            <div class="findsomething_title" id="popupMobile">手机号</div><button class="copy" name="mobile">复制</button>
            <p id="mobile" style="">🈚️</p>
            <div class="findsomething_title" id="popupMail">邮箱</div><button class="copy" name="mail">复制</button>
            <p id="mail" style="">🈚️</p>
            <div class="findsomething_title" id="popupJwt">JWT<button class="copy" name="jwt">复制</button></div>
            <p id="jwt" style="word-break:break-word;">🈚️</p>
            <div class="findsomething_title" id="popupAlgorithm">算法</div><button class="copy" name="algorithm">复制</button>
            <p id="algorithm" style="">🈚️</p>
            <div class="findsomething_title" id="popupSecret">Secret</div><button class="copy" name="secret">复制</button>
            <p id="secret" style="">🈚️</p>
        </div>
        <div style="width: 480px; height: 800px; float: left; margin-left:16px;">
            <div class="findsomething_title" id="popupPath">Path</div><button id="path_button" class="copy" name="path">复制</button>
            <p id="path" style="">🈚️</p>
            <div class="findsomething_title" id="popupIncompletePath">IncompletePath</div><button class="copy" name="incomplete_path">复制</button>
            <p id="incomplete_path" style="">🈚️</p>
            <div class="findsomething_title" id="popupUrl">Url</div><button class="copy" name="url">复制</button>
            <p id="url" style="">🈚️</p>
            <div class="findsomething_title" id="popupStaticPath">StaticUrl</div><button class="copy" name="static">复制</button>
            <p id="static" style="">🈚️</p>
        </div>
</div>
</body>
<script>
    
    // @Date    : 2020-09-12 16:26:48
    // <AUTHOR> residuallaugh

    var key = ["ip","ip_port","domain","path","incomplete_path","url","static","sfz","mobile","mail","jwt","algorithm","secret"]

    let messages = {
        "popupCopy": "复制",
        "popupCopyurl": "复制URL",
        "popupTipClickBeforeCopy": "请点击原页面后再复制：）"
    };

    function getMessage(key) {
        return messages[key] || "";
    }

    function init_copy() {
        var elements = document.getElementsByClassName("copy");
        if(elements){
            for (var i=0, len=elements.length|0; i<len; i=i+1|0) {
                elements[i].textContent = getMessage("popupCopy");
                let ele_name = elements[i].name;
                let ele_id = elements[i].id;
                if (ele_id == "popupCopyurl"){
                    elements[i].textContent = getMessage("popupCopyurl");
                }
                elements[i].onclick=function () {
                    var inp =document.createElement('textarea');
                    document.body.appendChild(inp)
                    var copytext = document.getElementById(ele_name).textContent;
                    inp.value = copytext;
                    inp.select();
                    document.execCommand('copy',false);
                    inp.remove();
                }
            }
        }
    };

    function show_info(result_data) {
        for (var k in key){
            if (result_data[key[k]]){
                let container = document.getElementById(key[k]);
                while((ele = container.firstChild)){
                    ele.remove();
                }
                container.style.whiteSpace = "pre";
                for (var i in result_data[key[k]]){
                    let tips = document.createElement("div");
                    tips.setAttribute("class", "tips")
                    let link = document.createElement("a");
                    link.appendChild(tips);
                    let span = document.createElement("span");
                    span.textContent = result_data[key[k]][i]+'\n';
                    container.appendChild(link);
                    container.appendChild(span);
                }
            }
        }
    }

    init_copy();

    show_info({"sfz":[],"mobile":[],"mail":["<EMAIL>"],"ip":["127.0.0.1"],"ip_port":[],"domain":["http://commonmark.org","http://www.w3.org","https://twitter.com","https://twitter.com/","https://github.com","https://kennethreitz.org","https://fonts.googleapis.com"],"path":["/dist","/*","/./","/debug?url=","/a","/em","/strong","/p","/code","/pre","/blockquote","/li","/=","/forms/post"],"incomplete_path":["*/","application/json","#/definitions/","#/components/schemas/","components/schemas","components/responses","components/parameters","components/securitySchemes","://","#/","text/html","Chrome/66","text/xml","text/plain","text/css","image/png","text/javascript"],"url":["http://www.w3.org/1999/xhtml","http://www.w3.org/1998/Math/MathML","http://www.w3.org/2000/svg","https://twitter.com/","http://www.w3.org/1999/xlink","http://commonmark.org/xml/1.0","http://www.w3.org/XML/1998/namespace","https://fonts.googleapis.com/css?family=Open+Sans:400,700|Source+Code+Pro:300,600|Titillium+Web:400,600,700","https://github.com/requests/httpbin","https://kennethreitz.org","https://github.com/rochacbruno/flasgger"],"jwt":[],"algorithm":["?atob("],"secret":["token:a","token:t","tokens=i","Token=function","DirectiveToken=function","DocumentStartToken=function","DocumentEndToken=function","StreamStartToken=function","StreamEndToken=function","BlockSequenceStartToken=function","BlockMappingStartToken=function","BlockEndToken=function","FlowSequenceStartToken=function","FlowMappingStartToken=function","FlowSequenceEndToken=function","FlowMappingEndToken=function","KeyToken=function","ValueToken=function","BlockEntryToken=function","FlowEntryToken=function","AliasToken=function","AnchorToken=function","TagToken=function","ScalarToken=function","tokenUrl:l","unescapeJsonPointerToken:p","token_number=e","tokens_taken=0","check_token=function","peek_token=function","get_token=function","need_more_tokens=function","fetch_more_tokens=function","scan_to_next_token=function","tokens=p","tokenize=function","nextJSXToken=function","peekJSXToken=function","tokens:\"boolean\"","unexpectedTokenError=function","throwUnexpectedToken=function","tolerateUnexpectedToken=function","getTokenRaw=function","convertToken=function","nextToken=function","nextRegexToken=function","UnexpectedToken:o","UnexpectedToken:\"Unexpected","UnexpectedTokenIllegal:\"Unexpected","getNextToken=function","Tokenizer=s","skipToken=function","tokens=e","tokens:e","tokens=n","client_secret=p","client_secret:s","clientSecret:g","accessKey:0","username:a","username:n","username:s","username=g","username=f","username:c","username:m","authorizePassword=t","authorizePassword=function","password:s","password:i","password=f","passwordType:y","\"password\":\"text\"","\"password\":return","Password=t","Password=function","ApiKey=p","\"apiKey\":c","NODE_ENV:\"production\"","tokens=h","Tokenizer=a"],"static":["./index.js","./all.js","./ast/ast.js","./ast/index.js","./ast/jump-to-path.jsx","./auth/actions.js","./auth/index.js","./auth/reducers.js","./auth/selectors.js","./auth/spec-wrap-actions.js","./configs/actions.js","./configs/helpers.js","./configs/index.js","./configs/reducers.js","./configs/selectors.js","./configs/spec-actions.js","./deep-linking/helpers.js","./deep-linking/index.js","./deep-linking/layout-wrap-actions.js","./deep-linking/spec-wrap-actions.js","./download-url.js","./err/actions.js","./err/error-transformers/hook.js","./err/error-transformers/transformers/not-of-type.js","./err/error-transformers/transformers/parameter-oneof.js","./err/error-transformers/transformers/strip-instance.js","./err/index.js","./err/reducers.js","./err/selectors.js","./filter/index.js","./filter/opsFilter.js","./layout/actions.js","./layout/index.js","./layout/reducers.js","./layout/selectors.js","./logs/index.js","./oas3/actions.js","./oas3/auth-extensions/wrap-selectors.js","./oas3/components/callbacks.jsx","./oas3/components/http-auth.jsx","./oas3/components/index.js","./oas3/components/operation-link.jsx","./oas3/components/operation-servers.jsx","./oas3/components/request-body-editor.jsx","./oas3/components/request-body.jsx","./oas3/components/servers.jsx","./oas3/helpers.js","./oas3/index.js","./oas3/reducers.js","./oas3/selectors.js","./oas3/spec-extensions/selectors.js","./oas3/spec-extensions/wrap-selectors.js","./oas3/wrap-components/auth-item.jsx","./oas3/wrap-components/index.js","./oas3/wrap-components/markdown.js","./oas3/wrap-components/model.jsx","./oas3/wrap-components/online-validator-badge.js","./oas3/wrap-components/parameters.jsx","./oas3/wrap-components/version-stamp.jsx","./on-complete/index.js","./samples/fn.js","./samples/index.js","./spec/actions.js","./spec/index.js","./spec/reducers.js","./spec/selectors.js","./spec/wrap-actions.js","./split-pane-mode/components/split-pane-mode.jsx","./split-pane-mode/index.js","./swagger-js/index.js","./util/index.js","./view/index.js","./view/root-injects.js","/spec.json","/flasgger_static/swagger-ui.css","/static/favicon.ico","/flasgger_static/swagger-ui-bundle.js","/flasgger_static/swagger-ui-standalone-preset.js","/flasgger_static/lib/jquery.min.js"]})
</script>
<style type="text/css">
    .copy {
        border-style: none;
        background-color: #ffffff;
        float: right;
        margin-right: 16px;
        
    }
    .findsomething_title {
        font-size: 16px;
        font-weight: bold;
        border-left: 4px solid black;
        text-indent: 4px;
        height: 16px;
        line-height: 16px;
    }
    .tips {
        display: inline-block;
        border-top: 0.2px solid;
        border-right: 0.2px solid;
        width: 10px;
        height: 10px;
        border-color: #EA6000;
        transform: rotate(-135deg);
    }
    a{
        text-decoration:none;
        color:#333;
    }
    button{
        cursor: pointer
    }
</style>
</html>